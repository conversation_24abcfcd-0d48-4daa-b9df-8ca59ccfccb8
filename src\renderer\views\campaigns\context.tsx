import React, {
  createContext,
  useState,
  useContext,
  useMemo,
  useCallback,
  useEffect,
} from 'react';
import { Campaign } from '../../../interfaces/Campaigns';

type CampaignProviderProps = {
  children: React.ReactNode;
};

interface CampaignContextType {
  campaigns: Campaign[];
  loading: boolean;
  getAllCampaigns: (data: Campaign[]) => void;
  addCampaign: (campaign: Campaign) => void;
  updateCampaign: (id: string, campaign: Campaign) => void;
  deleteCampaign: (id: string) => void;
}

/**
 * Fetch campaigns
 */
async function fetchCampaigns() {
  const response = await window.Campaigns.getAllCampaigns();
  return response;
}

const CampaignContext = createContext<CampaignContextType | undefined>(
  undefined,
);

export function CampaignProvider({ children }: CampaignProviderProps) {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState<boolean>(true); // ✅ Start with loading true

  useEffect(() => {
    const loadCampaigns = async () => {
      setLoading(true);
      try {
        const response = await fetchCampaigns();
        setCampaigns(response);
      } catch (error) {
        console.error('CampaignContext: Error fetching campaigns:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCampaigns();
  }, []);

  const getAllCampaigns = useCallback((data: Campaign[]) => {
    setLoading(true);
    setCampaigns(data);
    setLoading(false);
  }, []);

  const addCampaign = useCallback((campaign: Campaign) => {
    setCampaigns((prev) => [...prev, { ...campaign }]);
  }, []);

  const updateCampaign = useCallback(
    (id: string, updatedCampaign: Campaign) => {
      setCampaigns((prev) => {
        const updated = prev.map((camp) =>
          camp.id === id ? { ...updatedCampaign, id } : camp,
        );
        return updated;
      });
    },
    [],
  );

  const deleteCampaign = useCallback((id: string) => {
    setCampaigns((prev) => prev.filter((camp) => camp.id !== id));
  }, []);

  const contextValues = useMemo(
    () => ({
      loading,
      campaigns,
      getAllCampaigns,
      addCampaign,
      updateCampaign,
      deleteCampaign,
    }),
    [
      loading,
      campaigns,
      getAllCampaigns,
      addCampaign,
      updateCampaign,
      deleteCampaign,
    ],
  );

  return (
    <CampaignContext.Provider value={contextValues}>
      {children}
    </CampaignContext.Provider>
  );
}

export const useCampaignContext = () => {
  const context = useContext(CampaignContext);
  if (!context) {
    throw new Error(
      'useCampaignContext must be used within an CampaignProvider',
    );
  }
  return context;
};
