import React from 'react';
import { Link, Typography, Chip } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { Column } from '../../../components/table/TableConfig';
import { Campaign } from '../../../../interfaces/Campaigns';
import { formatDate } from '../../../../utils/format';
import StatusChip from '../../../components/StatusChip';

/**
 * Get campaign table columns configuration
 * @returns Array of column configurations for campaign table
 */
export const getCampaignTableColumns = (): Column<Campaign>[] => [
  {
    key: 'name',
    text: 'Tên chiến dịch',
    sortable: true,
    render: (value: string, item) => (
      <Link
        component={RouterLink}
        to={`/campaign/detail/${item.id}`}
        variant="body1"
      >
        {value}
      </Link>
    ),
  },
  {
    key: 'created_at',
    text: '<PERSON><PERSON>y tạo',
    sortable: true,
    render: (value: string) => (
      <Typography variant="body2" color="text.secondary">
        {formatDate(value, 'datetime')}
      </Typography>
    ),
  },
  {
    key: 'type',
    text: 'Loại chiến dịch',
    sortable: false,
    render: (value: string) => (
      <Chip
        label={value}
        variant="outlined"
        size="small"
        sx={{
          fontSize: '0.75rem',
          borderRadius: 2,
          fontWeight: 600,
          color: '#c8975d',
        }}
      />
    ),
  },
  {
    key: 'status',
    text: 'Trạng thái',
    sortable: false,
    render: (value: string) => <StatusChip status={value} />,
  },
];
