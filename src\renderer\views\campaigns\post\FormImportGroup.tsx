/* eslint-disable @typescript-eslint/no-unused-vars */
import { ChangeEvent, useState } from "react";
import Box from "@mui/material/Box";
import { Typography } from "@mui/material";
import { CampaignConfigProps } from "../CampaignConfig";
import InputText from "../../../components/form/InputText";
import InputFileUpload from "../../../components/buttons/ButtonUploadFile";
import FormWrapper from "../common/FormWrapper";
import { processUploadedFile } from "../../../../utils/file";
import { useAlert } from "../../../hooks/AlertContext";


export default function FormImportGroup({ config, onChange, errors }: CampaignConfigProps) {
  
  const { showAlert } = useAlert();
  const [uidString, setUidString] = useState<string>(config.groupIds?.join('\n') || '');
  const [countUids, setCountUids] = useState<number>(config.groupIds?.length || 0);
  const maxFileSize = 5; // 5MB

  
  const onChangeGroupIds = (value: string) => {
    const uidsArray = value.split('\n').map(uid => uid.trim());
    const uniqueUids = Array.from(new Set(uidsArray));
    setCountUids(uniqueUids.length);
    setUidString(uniqueUids.join('\n')); // Join back to string for display
    onChange('groupIds', uniqueUids, 'textarea');
  }


  /**
   * @param e ChangeEvent<HTMLInputElement>
   * Handle file upload and append data to text area
   * @returns 
   */
  const handleUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    processUploadedFile(
      maxFileSize,
      file,
      (uidSet) => {
        const newStr = uidString ? `${uidString}\n${uidSet.join('\n')}` : uidSet.join('\n');
        onChangeGroupIds(newStr);
      },
      (errorMessage) => {
        showAlert(errorMessage, 'error');
      }
    );
  }

  return (
    <FormWrapper title="Danh sách nhóm" description="Nhập UID nhóm hoặc tải tệp Excel/CSV hoặc TXT danh sách UID">
      <Box m={2} display="flex" flexDirection="column" gap={1}>
        <InputText
        id='groupIds'
        label='Danh sách UID nhóm'
        name='groupIds'
        type='textarea'
        value={uidString}
        onChange={ e => onChangeGroupIds(e.target.value) }
        multiline
        rows={4}
        placeholder="Danh sách UID nhóm (phân tách bằng Enter)"
        errorMessage={errors?.groupIds}
        style={{paddingTop: '10px'}}
        />
        {config.groupIds && 
          <div>
            <Typography variant="subtitle1" color="textSecondary" textAlign="right">
            Số lượng UID đã nhập: <strong>{countUids}</strong>
            </Typography>
          </div>
        }
      </Box>

      <Box m={2}>
        <InputFileUpload
          id="fileUpload"
          name="fileUpload"
          title="Tải tệp danh sách UID"
          accept=".txt, .csv, xls, .xlsx"
          onChange={handleUpload}
          maxLength={50}
        />
      </Box>
      
    </FormWrapper>
  );
}