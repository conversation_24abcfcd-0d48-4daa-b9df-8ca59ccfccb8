import { useState, useEffect, useCallback, useMemo } from 'react';
import { Campaign } from '../../../../interfaces/Campaigns';
import { CampaignType } from '../CampaignConfig';
import { useCampaignContext } from '../context';
import { filterCampaigns } from '../../../../utils/campaignStatus';

interface UseCampaignSearchProps {
  type?: CampaignType;
}

interface UseCampaignSearchReturn {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filteredCampaigns: Campaign[];
  handleSearch: (value: string) => Promise<void>;
}

/**
 * Custom hook for campaign search and filtering functionality
 */
export const useCampaignSearch = ({
  type,
}: UseCampaignSearchProps): UseCampaignSearchReturn => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { campaigns, getAllCampaigns } = useCampaignContext();

  // Filter campaigns based on search query
  const filteredCampaigns = useMemo(() => {
    return filterCampaigns(campaigns, searchQuery);
  }, [campaigns, searchQuery]);

  // Search handler
  const handleSearch = useCallback(
    async (value: string) => {
      const response = await window.Campaigns.getAllCampaigns();
      getAllCampaigns(response);
    },
    [getAllCampaigns],
  );

  // Effect for debounced search
  useEffect(() => {
    if (searchQuery.trim().length > 0) {
      const timeoutId = setTimeout(() => handleSearch(searchQuery), 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [handleSearch, searchQuery]);

  // Effect for loading campaigns by type
  useEffect(() => {
    if (type) {
      window.Campaigns.getAllCampaigns(type)
        .then((response) => {
          getAllCampaigns(response);
          return response;
        })
        .catch(() => []);
    }
  }, [getAllCampaigns, type]);

  return {
    searchQuery,
    setSearchQuery,
    filteredCampaigns,
    handleSearch,
  };
};
