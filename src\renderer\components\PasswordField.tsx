import React from 'react';
import { Box, Typography, IconButton, Tooltip, useTheme } from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { maskPassword, getPasswordFieldStyle } from '../../utils/accountStatus';

interface PasswordFieldProps {
  password: string;
  isVisible: boolean;
  onToggleVisibility: () => void;
  sx?: object;
}

/**
 * Reusable password field component with toggle visibility
 * @param password - Password value to display
 * @param isVisible - Whether password is currently visible
 * @param onToggleVisibility - Function to toggle password visibility
 * @param sx - Additional styles
 */
const PasswordField: React.FC<PasswordFieldProps> = ({
  password,
  isVisible,
  onToggleVisibility,
  sx = {},
}) => {
  const theme = useTheme();
  const styles = getPasswordFieldStyle(theme, isVisible);
  const displayPassword = isVisible ? password : maskPassword(password);

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ...sx }}>
      <Box sx={styles.container}>
        <Typography variant="body2" sx={styles.text}>
          {displayPassword}
        </Typography>
      </Box>
      <Tooltip title={isVisible ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}>
        <IconButton size="small" onClick={onToggleVisibility} sx={styles.button}>
          {isVisible ? (
            <VisibilityOffIcon fontSize="small" />
          ) : (
            <VisibilityIcon fontSize="small" />
          )}
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default PasswordField;
