/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Typography,
  Button,
  Stack,
  useTheme,
  alpha,
  Fade,
  LinearProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Save as SaveIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { FormEvent, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { CampaignType, formPostInit, InputConfig } from '../CampaignConfig';
import FormContent from '../common/FormContent';
import FormPostGroupConfig from './FormPostGroupConfig';
import FormImportGroup from './FormImportGroup';
import useMultiplestepForm from '../../../hooks/useMultiplestepForm';
import CommonValidation from '../../../../utils/validation';
import {
  CampaignConfigurationRequest,
  CampaignDetails,
  CampaignDetailsRequest,
  CampaignRequest,
  GroupRequest,
  ImageRequest,
} from '../../../../interfaces/Campaigns';
import { useCampaignContext } from '../context';
import { useAlert } from '../../../hooks/AlertContext';
import { messages } from '../../../../utils/messages';

const campaignSteps = ['Nội dung', 'Cấu hình chiến dịch', 'Nhập danh sách'];

const getCampaignSteps = (campaignDetails: CampaignDetails): number => {
  if (campaignDetails.Campaign && campaignDetails.Campaign.id) {
    return 1;
  }
  if (campaignDetails.Group && campaignDetails.Group.length > 0) {
    return 2;
  }

  return 0;
};

function FormPostStepper() {
  const theme = useTheme();
  const {
    currentStep,
    isFirstStep,
    isLastStep,
    setCurrentStep,
    nextStep,
    previousStep,
  } = useMultiplestepForm(campaignSteps.length);
  const { id } = useParams<{ id?: string }>();
  const [formData, setFormData] =
    useState<CampaignDetailsRequest>(formPostInit);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { addCampaign, updateCampaign } = useCampaignContext();
  const { showAlert } = useAlert();
  const navigate = useNavigate();

  /**
   * Fetch campaign details if editing
   */
  useEffect(() => {
    if (id) {
      const fetchCampaignDetails = async () => {
        try {
          setIsLoading(true);
          const response = await window.Campaigns.getCampaignDetails(id);
          if (response?.Campaign && response.Campaign) {
            console.log('response', response);
            setFormData((prev) => ({
              ...prev,
              id,
              name: response.Campaign?.name || '',
              delay: response.CampaignConfiguration?.delay || 5,
              max_post: response.CampaignConfiguration?.max_post || 1,
              comment_after_post:
                response.CampaignConfiguration?.comment_after_post || false,
              comment_content:
                response.CampaignConfiguration?.comment_content || '',
              is_anonymous:
                response.CampaignConfiguration?.is_anonymous || false,
              is_joingroup:
                response.CampaignConfiguration?.is_joingroup || false,
              tag_friend: response.CampaignConfiguration?.tag_friend || false,
              message: response.Campaign?.message || '',
              tagFollowers:
                response.CampaignConfiguration?.tagFollowers || false,
              tagNeubat: response.CampaignConfiguration?.tagNeubat || false,
              groupIds: response.Group?.map((group) => group.groupID) || [],
              imagePaths: response.Image?.map((img) => img.image) || [],
            }));

            const initSteps = getCampaignSteps(response);
            setCurrentStep(initSteps);
          }
        } catch (error) {
          console.error('Error fetching campaign details:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCampaignDetails();
    }
  }, [id, setCurrentStep]);

  const handleChange = (
    name: keyof CampaignDetailsRequest,
    value: any,
    type: InputConfig['type'] = 'text',
  ) => {
    setFormData((prev) => {
      if (type === 'number') return { ...prev, [name]: Number(value) };
      if (type === 'checkbox') return { ...prev, [name]: value };
      return { ...prev, [name]: value };
    });
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <FormContent
            config={formData}
            onChange={handleChange}
            errors={errors}
          />
        );
      case 1:
        return (
          <FormPostGroupConfig
            config={formData}
            onChange={handleChange}
            errors={errors}
          />
        );
      case 2:
      default:
        return (
          <FormImportGroup
            config={formData}
            onChange={handleChange}
            errors={errors}
          />
        );
    }
  };

  const handleBack = () => {
    if (isFirstStep) {
      navigate('/campaign/post');
    } else {
      previousStep();
    }
  };

  /**
   * Validate specific step data ONLY
   * @param step - The step to validate (0, 1, or 2)
   * @returns boolean - true if has errors, false if no errors
   */
  const validateStepData = (step: number) => {
    const newErrors: string[] = [];
    const newFieldErrors: Record<string, string> = {};

    // Clear previous errors
    setErrors({});
    setValidationErrors([]);

    switch (step) {
      case 0: // Step 0: Nội dung - ONLY validate campaign name and content/images
        {
          // Validate campaign name
          const [nameRequired] = CommonValidation.requiredString(formData.name);
          if (nameRequired) {
            newErrors.push('Yêu cầu nhập tên chiến dịch');
            newFieldErrors.name = 'Yêu cầu nhập tên chiến dịch';
          }

          // Validate message or images
          const [messageRequired] = CommonValidation.requiredString(
            formData.message,
          );
          if (
            messageRequired &&
            (!formData.imagePaths || formData.imagePaths.length === 0)
          ) {
            newErrors.push(
              'Yêu cầu nhập nội dung hoặc hình ảnh cho chiến dịch',
            );
            newFieldErrors.message =
              'Yêu cầu nhập nội dung hoặc hình ảnh cho chiến dịch';
          }
        }
        break;

      case 1: // Step 1: Cấu hình chiến dịch - ONLY validate configuration settings
        // Validate delay (should be positive number)
        if (formData.delay < 0) {
          newErrors.push('Khoảng cách giữa các mỗi lần đăng phải lớn hơn 0');
          newFieldErrors.delay =
            'Khoảng cách giữa các mỗi lần đăng phải lớn hơn 0';
        }

        // Validate max_post (should be positive number)
        if (formData.max_post < 1) {
          newErrors.push('Số bài tối đa phải lớn hơn 0');
          newFieldErrors.max_post = 'Số bài tối đa phải lớn hơn 0';
        }

        // Validate comment content if comment after post is enabled
        if (
          formData.comment_after_post &&
          (!formData.comment_content || formData.comment_content.trim() === '')
        ) {
          newErrors.push(
            'Nội dung bình luận được yêu cầu khi bật tính năng bình luận sau đăng',
          );
          newFieldErrors.comment_content = 'Nội dung bình luận được yêu cầu';
        }
        break;

      case 2: // Step 2: Nhập danh sách - ONLY validate group selection
        if (!formData.groupIds || formData.groupIds.length === 0) {
          newErrors.push('Yêu cầu nhập danh sách nhóm');
          newFieldErrors.groupIds = 'Yêu cầu nhập danh sách nhóm';
        }
        break;

      default:
        break;
    }

    // Update state with errors ONLY for current step
    if (newErrors.length > 0) {
      setValidationErrors(newErrors);
      setErrors(newFieldErrors);
      return true; // Has errors
    }

    console.log('erros', Object.values(errors));

    return false;
  };

  /**
   * Validate ALL steps for final submission
   * @returns boolean - true if has errors, false if no errors
   */
  const validateAllSteps = () => {
    // Check each step individually and collect all errors
    let hasAnyErrors = false;
    const allErrors: string[] = [];
    const allFieldErrors: Record<string, string> = {};

    // eslint-disable-next-line no-plusplus
    for (let step = 0; step < campaignSteps.length; step++) {
      // Validate this step
      const stepHasErrors = validateStepData(step);

      if (stepHasErrors) {
        hasAnyErrors = true;
        // Collect errors from this step
        allErrors.push(...validationErrors);
        Object.assign(allFieldErrors, errors);
      }
    }

    // Set all collected errors
    if (hasAnyErrors) {
      setValidationErrors(allErrors);
      setErrors(allFieldErrors);
    }

    return hasAnyErrors;
  };

  /**
   * Handles save campaign
   * @param form
   * Returns success status and campaign ID if created/updated successfully
   */
  const saveCampaign = async (
    form: CampaignDetailsRequest,
  ): Promise<{ success: boolean; campaignId?: string }> => {
    const campaignId = form.id;
    const { type, name, status, message } = form;
    const data: CampaignRequest = { name, type, message, status };

    console.log('Saving campaign', data);

    if (campaignId) {
      const response = await window.Campaigns.updateCampaign({
        id: campaignId,
        ...data,
      });
      if (response.success && response.data) {
        const updatedCampaign = response.data;
        updateCampaign(campaignId, updatedCampaign);
        return { success: true, campaignId };
      }

      showAlert(response.error || messages.general.error, 'error');
      return { success: false };
    }

    const response = await window.Campaigns.createCampaign(data);
    if (response.success && response.data) {
      const newCampaign = response.data;
      console.log('New campaign created', newCampaign);
      setFormData((prev) => ({ ...prev, id: newCampaign.id }));
      addCampaign(newCampaign);
      return { success: true, campaignId: newCampaign.id };
    }

    showAlert(response.error || messages.general.error, 'error');
    return { success: false };
  };

  /**
   * Saves campaign configuration
   * @param form
   */
  const saveCampaignConfig = async (
    campaignId: string,
    form: CampaignDetailsRequest,
  ): Promise<boolean> => {
    const data: CampaignConfigurationRequest = {
      campaignId,
      delay: form.delay < 0 ? 5 : form.delay,
      max_post: form.max_post < 0 ? 1 : form.max_post,
      comment_after_post: form.comment_after_post || false,
      comment_content: form.comment_content || '',
      is_anonymous: form.is_anonymous || false,
      is_joingroup: form.is_joingroup || false,
      tag_friend: form.tag_friend || false,
      tagFollowers: form.tagFollowers || false,
      tagNeubat: form.tagNeubat || false,
    };
    console.log('Saving campaign config', data);
    const response = await window.Campaigns.saveCampaignConfig(data);
    if (response.error) {
      showAlert(response.error || messages.general.error, 'error');
    }
    return response.success;
  };

  /**
   * Saves campaign group
   * @param form
   */
  const saveCampaignGroup = async (
    campaignId: string,
    form: CampaignDetailsRequest,
  ): Promise<boolean> => {
    const data: GroupRequest = {
      campaignId,
      groupIds: form.groupIds?.filter((uid) => uid.trim() !== '') || [],
    };
    console.log('Saving campaign groups', data);
    const response = await window.Campaigns.saveCampaignGroup(data);
    if (response.error) {
      showAlert(response.error || messages.general.error, 'error');
    }

    return response.success;
  };

  /**
   * Saves campaign image
   * @param form
   */
  const saveCampaignImage = async (
    campaignId: string,
    form: CampaignDetailsRequest,
  ): Promise<boolean> => {
    const data: ImageRequest = {
      imagePaths: form.imagePaths || [],
      campaignId,
    };
    const response = await window.Campaigns.saveCampaignImage(data);
    console.log('Saving campaign image', response);
    if (response.error) {
      showAlert(response.error || messages.general.error, 'error');
    }
    return response.success;
  };

  /**
   * Handles form submission for the current step
   * @param e - Form submit event
   * @returns
   */
  const handleOnSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // STEP 1: Validate ONLY the current step
    const hasCurrentStepErrors = validateStepData(currentStep);
    if (hasCurrentStepErrors) {
      return;
    }

    if (isLastStep) {
      const hasAllErrors = validateAllSteps();
      if (hasAllErrors) {
        showAlert(
          'Vui lòng hoàn tất các trường thông tin được yêu cầu',
          'error',
        );
        return;
      }

      // FINAL STEP: Save all data and finish
      setIsSubmitting(true);
      try {
        if (!formData.id) {
          showAlert('Không tìm thấy thông tin chiến dịch', 'error');
          return;
        }

        const isSavedGroup = await saveCampaignGroup(formData.id, formData);
        if (!isSavedGroup) return;

        setFormData(formPostInit);
        navigate('/campaign/post');
      } catch (error) {
        showAlert(messages.general.error, 'error');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // INTERMEDIATE STEPS: Save progress and advance to next step
      setIsSubmitting(true);
      try {
        if (isFirstStep) {
          console.log('first step');
          const isSavedCampaign = await saveCampaign(formData);
          if (!isSavedCampaign.success) return;

          if (
            isSavedCampaign.campaignId &&
            formData.imagePaths &&
            formData.imagePaths.length > 0
          ) {
            console.log('first step - save image', formData);
            const isSavedImage = await saveCampaignImage(
              isSavedCampaign.campaignId,
              formData,
            );
            if (!isSavedImage) return;
          }
        } else {
          console.log('second step');
          if (!formData.id) {
            showAlert('Không tìm thấy thông tin chiến dịch', 'error');
            return;
          }
          const isSavedConfig = await saveCampaignConfig(formData.id, formData);
          if (!isSavedConfig) return;
        }
        setErrors({});
        setValidationErrors([]);
        nextStep();
      } catch (error) {
        showAlert('Có lỗi xảy ra khi chuyển bước', 'error');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.5),
      }}
    >
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main || '#1976d2'} 0%, ${theme.palette.primary.dark || '#1565c0'} 100%)`,
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <Tooltip title="Quay lại danh sách">
            <IconButton
              onClick={() => navigate('/campaign/post')}
              sx={{
                color: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                },
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h3"
              sx={{ fontWeight: 600, mb: 0.5, color: 'white' }}
            >
              {id ? 'Chỉnh sửa chiến dịch' : 'Tạo chiến dịch mới'}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Chiến dịch đăng bài Facebook
            </Typography>
          </Box>
          <Chip
            icon={<InfoIcon />}
            label={`Bước ${currentStep + 1}/${campaignSteps.length}`}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              color: 'white',
              fontWeight: 600,
              '& .MuiChip-icon': {
                color: 'white',
              },
            }}
          />
        </Stack>
      </Paper>

      {/* Progress Bar */}
      <Paper elevation={1} sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <LinearProgress
          variant="determinate"
          value={((currentStep + 1) / campaignSteps.length) * 100}
          sx={{
            height: 6,
            backgroundColor: alpha(
              theme.palette.primary.main || '#1976d2',
              0.1,
            ),
            '& .MuiLinearProgress-bar': {
              backgroundColor: theme.palette.primary.main || '#1976d2',
            },
          }}
        />
        <Stepper activeStep={currentStep} sx={{ p: 3 }}>
          {campaignSteps.map((label, index) => (
            <Step key={label}>
              <StepLabel
                sx={{
                  '& .MuiStepLabel-label': {
                    fontWeight: currentStep === index ? 600 : 400,
                    color:
                      currentStep === index
                        ? theme.palette.primary.main
                        : 'text.secondary',
                  },
                }}
              >
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Fade in>
          <Alert
            severity="error"
            sx={{ mb: 3, borderRadius: 2 }}
            onClose={() => setValidationErrors([])}
          >
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Vui lòng kiểm tra các lỗi sau:
            </Typography>
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {validationErrors.map((error, index) => (
                <Typography key={index} component="li" variant="body2">
                  {error}
                </Typography>
              ))}
            </Box>
          </Alert>
        </Fade>
      )}

      {/* Main Content */}
      <Paper
        elevation={2}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          minHeight: '60vh',
        }}
      >
        <Box
          component="form"
          onSubmit={handleOnSubmit}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
          }}
        >
          {/* Form Content */}
          <Box sx={{ flex: 1, p: 4 }}>
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent(currentStep)}
            </motion.div>
          </Box>

          {/* Navigation Footer */}
          <Box
            sx={{
              p: 3,
              backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.8),
              borderTop: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Button
                onClick={handleBack}
                startIcon={<ArrowBackIcon />}
                variant="outlined"
                disabled={isSubmitting}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  py: 1,
                  visibility: isFirstStep ? 'hidden' : 'visible',
                }}
              >
                Quay lại
              </Button>

              <Button
                type="submit"
                variant="contained"
                disabled={isSubmitting}
                endIcon={isLastStep ? <SaveIcon /> : <ArrowForwardIcon />}
                sx={{
                  borderRadius: 2,
                  px: 4,
                  py: 1.5,
                  fontWeight: 600,
                  minWidth: 120,
                }}
              >
                {isSubmitting
                  ? 'Đang xử lý...'
                  : isLastStep
                    ? 'Lưu chiến dịch'
                    : 'Tiếp tục'}
              </Button>
            </Stack>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
}

export default FormPostStepper;
