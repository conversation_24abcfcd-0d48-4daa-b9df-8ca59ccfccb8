/* eslint-disable @typescript-eslint/no-unused-vars */
import { Database } from 'better-sqlite3';
import DatabaseManager from '../db/sqlite';
import {
  Campaign,
  Group,
  Image,
  CampaignDetails,
  CampaignConfiguration,
  User,
  UserGroup,
  CampaignRequest,
  CampaignDetailsForRun,
  CampaignConfigurationRequest,
} from '../interfaces/Campaigns';
import log from '../utils/logs';

export default class CampaignsServices {
  private db: Database;

  public static TYPE_POST = 'post';

  public static TYPE_COMMENT = 'comment';

  public static TYPE_PROFILE = 'profile';

  public static STATUS_NEW = 'new';

  public static STATUS_RUNNING = 'running';

  public static STATUS_STOPPED = 'stopped';

  public static STATUS_DONE = 'done';

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  createCampaign(data: CampaignRequest): number | null {
    try {
      const insertCampaign = this.db.prepare(`
        INSERT INTO campaigns (type, name, message, status)
        VALUES (?, ?, ?, ?)
      `);

      // Use provided status or default to 'new' if not provided
      const status = data.status || CampaignsServices.STATUS_NEW;

      const result = insertCampaign.run(
        data.type,
        data.name,
        data.message,
        status,
      );

      return result.lastInsertRowid as number;
    } catch (error) {
      log.error('An error occurred when create campaign', error);
      return null;
    }
  }

  getAllCampaigns(
    type?: string,
    page: number = 1,
    orderBy: keyof Campaign = 'created_at',
    order: 'ASC' | 'DESC' = 'DESC',
    limit: number = 25,
  ): Campaign[] {
    const offset = (page - 1) * limit;

    let query = `SELECT * FROM campaigns`;
    const params: any[] = [];
    if (type) {
      query += ` WHERE type = ?`;
      params.push(type);
    }

    query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const result = this.db.prepare(query).all(...params) as Campaign[];

    return result;
  }

  /**
   * Get all valid data for a campaign to run
   * valid data includes:
   * - CampaignId
   * - Campaign configuration
   * - Group IDs length > 0
   * @param id
   * @returns
   */
  getDataForRunCampaign(id: string): CampaignDetailsForRun | null {
    try {
      const campaign = this.getCampaign(id);
      if (!campaign) {
        log.error(`Không tìm thấy chiến dịch với ID ${id}`);
        return null;
      }

      const configuration = this.db
        .prepare(`SELECT * FROM campaignconfiguration WHERE campaign_id = ?`)
        .get(id) as CampaignConfigurationRequest;

      if (!configuration) {
        log.error(`Không tìm thấy cấu hình chiến dịch với ID ${id}`);
        return null;
      }

      const groups = this.db
        .prepare(
          `SELECT * FROM groups WHERE campaign_id = ? AND status != 'done' AND status != 'false'`,
        )
        .all(id) as Group[];

      if (!groups || groups.length === 0) {
        log.error(`Không tìm thấy danh sách nhóm để chạy chiến dịch`);
        return null;
      }

      const groupIds = groups.map((group) => group.groupID);

      const users = this.db
        .prepare(`SELECT * FROM User WHERE campaign_id = ?`)
        .all(id) as User[];

      const images = this.db
        .prepare(`SELECT * FROM Image WHERE campaign_id = ?`)
        .all(id) as Image[];

      const imagePaths = images.map((image) => image.image);

      return {
        Campaign: campaign,
        CampaignConfiguration: configuration,
        groupIds: groupIds || [],
        imagePaths: imagePaths || [],
        User: users || [],
      };
    } catch (error) {
      log.error(
        `An error occurred when fetching campaign details for id ${id}`,
        error,
      );
      return null;
    }
  }

  getUserGroup(CampaignId: string, groupID: string): UserGroup[] | null {
    try {
      const group = this.db
        .prepare('SELECT id FROM groups WHERE campaign_id = ? AND groupID = ?')
        .get(CampaignId, groupID) as { id: number } | undefined;
      if (!group) {
        return null;
      }

      const userGroups = this.db
        .prepare('SELECT * FROM UserGroup WHERE groupId = ?')
        .all(group.id) as UserGroup[];

      return userGroups;
    } catch (error) {
      log.error('Error fetching UserGroup data:', error);
      return null;
    }
  }

  /**
   * Deletes a campaign and ON DELETE CASCADE relative tables
   * @param id id của chiến dịch
   */
  deleteCampaign(id: number): boolean {
    try {
      this.db.prepare(`DELETE FROM campaigns WHERE id = ?`).run(id);
      return true;
    } catch (error) {
      log.error(`An error occurred when delete campaign id ${id}`, error);
      return false;
    }
  }

  /**
   * Deletes multiple campaigns by their IDs
   * @param ids Array of campaign IDs to delete
   */
  bulkDeleteCampaigns(ids: string[]): boolean {
    log.info(`Bulk deleting campaigns with IDs: ${ids.join(', ')}`);
    try {
      const result = this.db.transaction(() => {
        const stmt = this.db.prepare(
          `DELETE FROM campaigns WHERE id IN (${ids.map(() => '?').join(',')})`,
        );
        return stmt.run(ids);
      })();
      return true;
    } catch (error) {
      log.error(`An error occurred when deleting campaigns`, error);
      return false;
    }
  }

  updateCampaign(campaignId: string, data: CampaignRequest): boolean {
    const campaign = this.getCampaign(campaignId);
    if (!campaign) {
      return false;
    }

    try {
      this.db
        .prepare(
          `
        UPDATE campaigns
        SET name = ?, status = ?, message = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `,
        )
        .run(data.name, data.status, data.message, campaignId);
      return true;
    } catch (error) {
      log.error(
        `An error occurred when update campaign id ${campaignId}`,
        error,
      );
      return false;
    }
  }

  updateCampaignStatus(CampaignId: string, status: string): boolean {
    try {
      const isUpdated =
        this.db
          .prepare(
            `
      UPDATE campaigns
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `,
          )
          .run(status, CampaignId).changes > 0;
      if (!isUpdated) {
        log.warn(
          `No campaign found with id ${CampaignId} to update status to ${status}`,
        );
        return false;
      }
      return true;
    } catch (error) {
      log.error(
        `An error occurred when update campaign status id ${CampaignId}`,
        error,
      );
      return false;
    }
  }

  // tao danh sách tai khoan se chạy cho chiên dich
  create_listUser(campaignId: string, profileId: string): number | null {
    try {
      const rowId = this.db
        .prepare(
          `
      INSERT INTO User (campaign_id, profileId, numbersend)
      VALUES (?, ?, ?)
    `,
        )
        .run(campaignId, profileId, 0).lastInsertRowid as number;
      if (!rowId) {
        log.warn(
          `User with profileId ${profileId} already exists for campaign ${campaignId}`,
        );
        return null;
      }
      return rowId;
    } catch (error) {
      log.error(
        `An error occurred when create list user for campaign id ${campaignId}`,
        error,
      );
      return null;
    }
  }

  // câp nhập số lượt đã chạy của tài khoan chien dich do
  update_listUser({ campaign_id, profileId, numbersend }: User): boolean {
    try {
      const isUpdated =
        this.db
          .prepare(
            `
      UPDATE User
      SET numbersend = ?
      WHERE campaign_id = ? AND profileId = ?
    `,
          )
          .run(numbersend || 0, campaign_id, profileId).changes > 0;
      if (!isUpdated) {
        log.warn(
          `No user found with profileId ${profileId} for campaign ${campaign_id}`,
        );
        return false;
      }
      return true;
    } catch (error) {
      log.error(
        `An error occurred when update list user for campaign id ${campaign_id}`,
        error,
      );
      return false;
    }
  }

  // cap nhap trang thai cho tung nhom trong tung chien dich
  updateListGroupStatus({
    campaign_id,
    groupID,
    status,
    postId,
  }: Group): boolean {
    try {
      const isUpdated =
        this.db
          .prepare(
            `
      UPDATE groups
      SET status = ?, postId = ?
      WHERE campaign_id = ? AND groupID = ?
    `,
          )
          .run(status, postId, campaign_id, groupID).changes > 0;
      if (!isUpdated) {
        log.warn(
          `No group found with groupID ${groupID} for campaign ${campaign_id}`,
        );
        return false;
      }
      return true;
    } catch (error) {
      log.error(
        `An error occurred when update group status for campaign id ${campaign_id}`,
        error,
      );
      return false;
    }
  }

  getCampaign(id: string): Campaign | null {
    try {
      return this.db
        .prepare(`SELECT * FROM campaigns WHERE id = ?`)
        .get(id) as Campaign;
    } catch (error) {
      return null;
    }
  }

  inserUserGroup(
    CampaignId: string,
    { groupId, userpost, profileId, status }: UserGroup,
  ): boolean {
    let groupIdRow;
    try {
      groupIdRow = this.db
        .prepare(
          `
        SELECT id FROM groups WHERE campaign_id = ? AND groupID = ?
      `,
        )
        .get(CampaignId, groupId) as { id: number } | undefined;
    } catch (error) {
      log.error(
        `An error occurred when fetching id for campaign ${CampaignId} and groupID ${groupId}`,
        error,
      );
      return false;
    }
    if (!groupIdRow) {
      log.error(
        `Không tìm thấy nhóm với campaign_id=${CampaignId} và groupID=${groupId}`,
      );
      return false;
    }

    try {
      const isCreated =
        this.db
          .prepare(
            `
        INSERT INTO UserGroup (groupId, userpost, profileId, status)
        VALUES (?, ?, ?, ?)
        `,
          )
          .run(groupIdRow.id, userpost, profileId, status).changes > 0;

      if (!isCreated) {
        log.warn(
          `UserGroup with groupId ${groupIdRow.id} and profileId ${profileId} already exists`,
        );
        return false;
      }
      return true;
    } catch (error) {
      log.error(
        `An error occurred when inserting UserGroup for campaign id ${CampaignId}`,
        error,
      );
      return false;
    }
  }

  getListUserByCampaignId(campaignId: string): User[] {
    try {
      return this.db
        .prepare(
          `
        SELECT * FROM User WHERE campaign_id = ?
      `,
        )
        .all(campaignId) as User[];
    } catch (error) {
      log.error(
        `An error occurred when fetching list user for campaign id ${campaignId}`,
        error,
      );
      return [];
    }
  }

  getuserpost(groupId: string): UserGroup {
    return this.db
      .prepare(
        `
      SELECT * FROM UserGroup WHERE groupId = ?
    `,
      )
      .get(groupId) as UserGroup;
  }
}
