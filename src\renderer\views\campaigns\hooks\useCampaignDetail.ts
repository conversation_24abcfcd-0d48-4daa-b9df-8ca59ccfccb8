import { useState, useEffect } from 'react';
import { CampaignDetails } from '../../../../interfaces/Campaigns';

interface ProgressLog {
  message: string;
  campaignId: string;
}

interface UseCampaignDetailReturn {
  campaignDetail: CampaignDetails | null;
  loading: boolean;
  error: string | null;
  logs: ProgressLog[];
  addLog: (newLog: ProgressLog) => void;
}

/**
 * Custom hook for campaign detail data management
 */
export const useCampaignDetail = (
  id: string | undefined,
): UseCampaignDetailReturn => {
  const [campaignDetail, setCampaignDetail] = useState<CampaignDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<ProgressLog[]>([]);

  const addLog = (newLog: ProgressLog) => {
    setLogs((prev) => [...prev, newLog]);
  };

  // Set up progress listener
  useEffect(() => {
    if (!id) return undefined;

    const cleanup = window.facebookWeb.onProgress((data) => {
      // Add new log to state
      addLog({
        message: data.message,
        campaignId: data.campaignId,
      });

      if (data.action === 'stopped') {
        // Handle campaign stopped
        console.log('Campaign stopped:', data.campaignId);
      }

      console.log('Progress message:', data.message);
    });

    return cleanup;
  }, [id]);

  // Fetch campaign detail data
  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await window.Campaigns.getCampaignDetails(id);
        setCampaignDetail(data);
      } catch (err) {
        console.error('Error fetching campaign details:', err);
        setError('Không thể tải thông tin chiến dịch. Vui lòng thử lại.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  return {
    campaignDetail,
    loading,
    error,
    logs,
    addLog,
  };
};
