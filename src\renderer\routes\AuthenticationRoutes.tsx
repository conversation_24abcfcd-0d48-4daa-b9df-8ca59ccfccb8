import { ComponentType, lazy } from 'react';
import Loadable from '../components/Loadable';
import MinimalLayout from '../layout/MinimalLayout';

const AuthLogin3 = Loadable(lazy(() => import('../views/pages/authentication3/Login3.tsx') as unknown as Promise<{ default: ComponentType<any>}>));
const AuthRegister3 = Loadable(lazy(() => import('../views/pages/authentication3/Register3.tsx') as unknown as Promise<{ default: ComponentType<any>}>));

const AuthenticationRoutes = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: '/pages/login/login3',
      element: <AuthLogin3 />
    },
    {
      path: '/pages/register/register3',
      element: <AuthRegister3 />
    }
  ]
};

export default AuthenticationRoutes;
