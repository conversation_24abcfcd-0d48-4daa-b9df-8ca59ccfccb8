import React, { useState } from 'react';
import {
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Typography,
  Tooltip,
  Alert,
  IconButton,
  <PERSON>lapse,
  Box,
  Chip,
  Stack,
  useTheme,
  alpha,
  Button,
} from '@mui/material';
import GroupIcon from '@mui/icons-material/Group';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { Group, UserGroup } from '../../../../interfaces/Campaigns';
import DetailInfoCard from '../../../components/DetailInfoCard';
import StatusChip from '../../../components/StatusChip';

interface CampaignGroupsSectionProps {
  groups: Group[] | null;
}

/**
 * Get user posting status configuration
 */
const getUserPostingStatusConfig = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'done':
      return {
        color: 'success' as const,
        icon: <CheckCircleIcon sx={{ fontSize: 16 }} />,
        label: 'Thành công',
      };
    case 'false':
      return {
        color: 'error' as const,
        icon: <ErrorIcon sx={{ fontSize: 16 }} />,
        label: 'Thất bại',
      };
    default:
      return {
        color: 'default' as const,
        icon: <PersonIcon sx={{ fontSize: 16 }} />,
        label: 'Không xác định',
      };
  }
};

/**
 * User posting status chip component
 */
const UserPostingStatusChip: React.FC<{ status: string }> = ({ status }) => {
  const config = getUserPostingStatusConfig(status);

  return (
    <Chip
      icon={config.icon}
      label={config.label}
      size="small"
      color={config.color}
      variant="outlined"
      sx={{ fontSize: '0.75rem', height: 24 }}
    />
  );
};

/**
 * User group row component
 */
const UserGroupRow: React.FC<{ userGroup: UserGroup; index: number }> = ({
  userGroup,
  index,
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 2,
        backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.5),
        borderRadius: 1,
        border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.3)}`,
        mb: 1,
      }}
    >
      <Stack direction="row" spacing={2} alignItems="center">
        <Box sx={{ flex: 1 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <PersonIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {userGroup.userpost || 'Unknown User'}
            </Typography>
          </Stack>
          {userGroup.profileId && (
            <Typography variant="caption" color="text.secondary">
              Profile ID: {userGroup.profileId}
            </Typography>
          )}
        </Box>
        <UserPostingStatusChip status={userGroup.status} />
      </Stack>
    </Box>
  );
};

/**
 * Campaign groups display section
 */
const CampaignGroupsSection: React.FC<CampaignGroupsSectionProps> = ({
  groups,
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  console.log('groups', groups);

  const toggleGroupExpansion = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const getUserPostingSummary = (userGroups: UserGroup[] | undefined) => {
    if (!userGroups || userGroups.length === 0) {
      return { total: 0, success: 0, failed: 0 };
    }

    const total = userGroups.length;
    const success = userGroups.filter(
      (ug) => ug.status?.toLowerCase() === 'done',
    ).length;
    const failed = userGroups.filter(
      (ug) => ug.status?.toLowerCase() === 'false',
    ).length;

    return { total, success, failed };
  };
  return (
    <DetailInfoCard
      title={`Danh sách nhóm (${groups?.length || 0})`}
      icon={<GroupIcon />}
      headerColor="#ff9800"
      iconColor="#ff9800"
    >
      {groups && groups.length > 0 ? (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <strong>Group ID</strong>
                </TableCell>
                <TableCell>
                  <strong>Post ID</strong>
                </TableCell>
                <TableCell>
                  <strong>Trạng thái</strong>
                </TableCell>
                <TableCell>
                  <strong>Người đăng</strong>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groups.map((group, index) => {
                const groupKey = group.id || `${group.groupID}-${index}`;
                const isExpanded = expandedGroups.has(groupKey);
                const summary = getUserPostingSummary(group.UserGroup);

                return (
                  <React.Fragment key={groupKey}>
                    <TableRow sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                      <TableCell>
                        <Tooltip title={group.groupID}>
                          <Typography
                            variant="body2"
                            noWrap
                            sx={{ maxWidth: 150 }}
                          >
                            {group.groupID}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        {group.postId ? (
                          <Tooltip title={group.postId}>
                            <Typography
                              variant="body2"
                              noWrap
                              sx={{ maxWidth: 150 }}
                            >
                              {group.postId}
                            </Typography>
                          </Tooltip>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Chưa đăng
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <StatusChip
                          status={group.status}
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {summary.total > 0 ? (
                          <Stack
                            direction="row"
                            spacing={1}
                            alignItems="center"
                          >
                            <Button
                              onClick={() => toggleGroupExpansion(groupKey)}
                            >
                              <Chip
                                label={`${summary.total} thành công`}
                                size="small"
                                variant="outlined"
                                color="primary"
                              />
                            </Button>
                          </Stack>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Chưa có dữ liệu
                          </Typography>
                        )}
                      </TableCell>
                    </TableRow>

                    {/* Expandable User Details Row */}
                    {group.UserGroup && group.UserGroup.length > 0 && (
                      <TableRow>
                        <TableCell colSpan={5} sx={{ p: 0, border: 'none' }}>
                          <Collapse
                            in={isExpanded}
                            timeout="auto"
                            unmountOnExit
                          >
                            <Box sx={{ p: 2, backgroundColor: 'grey.50' }}>
                              <Typography variant="subtitle2" gutterBottom>
                                Chi tiết người đăng ({group.UserGroup.length}{' '}
                                người):
                              </Typography>
                              <Stack spacing={1}>
                                {group.UserGroup.map((userGroup, userIndex) => (
                                  <UserGroupRow
                                    key={userGroup.id || userIndex}
                                    userGroup={userGroup}
                                    index={userIndex}
                                  />
                                ))}
                              </Stack>
                            </Box>
                          </Collapse>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Alert severity="info" icon={<GroupIcon />}>
          Không có nhóm nào được thêm vào chiến dịch này.
        </Alert>
      )}
    </DetailInfoCard>
  );
};

export default CampaignGroupsSection;
