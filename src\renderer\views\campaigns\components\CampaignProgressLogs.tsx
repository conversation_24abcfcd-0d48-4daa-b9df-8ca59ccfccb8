import React, { useRef, useEffect } from 'react';
import {
  Card,
  Box,
  Stack,
  Avatar,
  Typography,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  useTheme,
  alpha,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';

interface ProgressLog {
  message: string;
  campaignId: string;
}

interface CampaignProgressLogsProps {
  logs: ProgressLog[];
}

/**
 * Campaign progress logs display component
 */
const CampaignProgressLogs: React.FC<CampaignProgressLogsProps> = ({
  logs,
}) => {
  const theme = useTheme();
  const logEndRef = useRef<HTMLDivElement | null>(null);

  // Auto-scroll to bottom when new logs are added
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  return (
    <Card
      elevation={1}
      sx={{
        borderRadius: 3,
        overflow: 'hidden',
        position: 'sticky',
        top: 24,
        maxHeight: 'calc(100vh - 48px)',
      }}
    >
      <Box
        sx={{
          p: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main || '#4caf50', 0.1)} 0%, ${alpha(theme.palette.success.dark || '#388e3c', 0.05)} 100%)`,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Avatar
            sx={{
              width: 48,
              height: 48,
              backgroundColor: theme.palette.success.main || '#4caf50',
            }}
          >
            <InfoIcon />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
              Log thông tin
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Theo dõi tiến trình chiến dịch
            </Typography>
          </Box>
          <Chip
            label={`${logs.length} logs`}
            size="small"
            color="success"
            variant="outlined"
          />
        </Stack>
      </Box>

      <CardContent
        sx={{ p: 0, height: 'calc(100vh - 280px)', overflow: 'hidden' }}
      >
        <Box
          sx={{
            height: '100%',
            overflowY: 'auto',
            '&::-webkit-scrollbar': {
              width: 6,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: alpha(theme.palette.grey[300] || '#e0e0e0', 0.3),
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.5),
              borderRadius: 3,
              '&:hover': {
                backgroundColor: alpha(
                  theme.palette.grey[600] || '#757575',
                  0.7,
                ),
              },
            },
          }}
        >
          {logs.length > 0 ? (
            <List sx={{ p: 0 }}>
              {logs.map((log, index) => (
                <ListItem
                  key={index}
                  sx={{
                    borderBottom:
                      index < logs.length - 1
                        ? `1px solid ${theme.palette.divider}`
                        : 'none',
                    py: 1,
                    px: 2,
                    '&:hover': {
                      backgroundColor: alpha(
                        theme.palette.success.main || '#4caf50',
                        0.04,
                      ),
                    },
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        backgroundColor: alpha(
                          theme.palette.success.main || '#4caf50',
                          0.1,
                        ),
                        color: theme.palette.success.main || '#4caf50',
                        fontSize: '0.875rem',
                      }}
                    >
                      {index + 1}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 500,
                          lineHeight: 1.4,
                          wordBreak: 'break-word',
                        }}
                      >
                        {log.message}
                      </Typography>
                    }
                    secondary={
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ mt: 0.5, display: 'block' }}
                      >
                        {new Date().toLocaleTimeString('vi-VN')}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
              <span ref={logEndRef} />
            </List>
          ) : (
            <Box
              sx={{
                p: 4,
                textAlign: 'center',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <InfoIcon
                sx={{
                  fontSize: 48,
                  color: alpha(theme.palette.grey[400] || '#bdbdbd', 0.5),
                  mb: 2,
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Chưa có log nào
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Logs sẽ xuất hiện khi chiến dịch bắt đầu chạy
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default CampaignProgressLogs;
