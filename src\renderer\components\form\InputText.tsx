import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import { FormHelperText } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { InputTextProps } from './formType';

function InputText({
  id,
  label,
  name,
  type,
  errorMessage,
  multiline,
  ...rest
}: InputTextProps) {
  const theme = useTheme();
  return (
    <FormControl fullWidth sx={{ ...theme.typography.customInput }}>
      {multiline ? (
        <InputLabel htmlFor={id} style={{ marginTop: -8 }}>
          {label}
        </InputLabel>
      ) : (
        <InputLabel htmlFor={id}>{label}</InputLabel>
      )}
      <OutlinedInput
        id={id}
        type={type}
        name={name}
        label={label}
        multiline={multiline}
        {...rest}
      />
      {errorMessage && <FormHelperText error>{errorMessage}</FormHelperText>}
    </FormControl>
  );
}

InputText.defaultProps = {
  errorMessage: undefined,
};

export default InputText;
