import React, {
  createContext,
  useState,
  useContext,
  useMemo,
  useCallback,
  useEffect,
} from 'react';
import { IFBUserResponse } from '../../../interfaces/IFacebookUser';

type AccountProviderProps = {
  children: React.ReactNode;
};

interface AccountContextType {
  accounts: IFBUserResponse[];
  loading: boolean;
  getAllAccounts: (data: IFBUserResponse[]) => void;
  addAccount: (account: IFBUserResponse) => void;
  updateAccount: (id: string, account: IFBUserResponse) => void;
  deleteAccount: (id: string) => void;
}

/**
 * Fetch accounts
 */
async function fetchAccounts() {
  const response = await window.account.getAllUsers();
  return response;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export function AccountProvider({ children }: AccountProviderProps) {
  const [accounts, setAccounts] = useState<IFBUserResponse[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    setTimeout(async () => {
      try {
        const response = await fetchAccounts();
        setAccounts(response);
      } catch (error) {
        console.error('AccountContext: Error fetching accounts:', error);
      } finally {
        setLoading(false);
      }
    }, 500);
  }, []);

  const getAllAccounts = useCallback((data: IFBUserResponse[]) => {
    setLoading(true);
    setTimeout(() => {
      setAccounts(data);
      setLoading(false);
    }, 500);
  }, []);

  const addAccount = useCallback((account: IFBUserResponse) => {
    setAccounts((prev) => [...prev, { ...account }]);
  }, []);

  const updateAccount = useCallback(
    (id: string, updatedAccount: IFBUserResponse) => {
      setAccounts((prev) =>
        prev.map((acc) => (acc.id === id ? { ...updatedAccount, id } : acc)),
      );
    },
    [],
  );

  const deleteAccount = useCallback((id: string) => {
    setAccounts((prev) => prev.filter((acc) => acc.id !== id));
  }, []);

  const contextValues = useMemo(
    () => ({
      loading,
      accounts,
      getAllAccounts,
      addAccount,
      updateAccount,
      deleteAccount,
    }),
    [
      loading,
      accounts,
      getAllAccounts,
      addAccount,
      updateAccount,
      deleteAccount,
    ],
  );

  return (
    <AccountContext.Provider value={contextValues}>
      {children}
    </AccountContext.Provider>
  );
}

export const useAccountContext = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccountContext must be used within an AccountProvider');
  }
  return context;
};
